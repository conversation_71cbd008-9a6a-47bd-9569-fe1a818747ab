#!/usr/bin/env python3
"""
Scrip<PERSON> to check the current ingestion status and vector store contents.
"""

import os
from dotenv import load_dotenv
from vectorstore_utils import load_vectorstore_client
import boto3

def check_s3_files():
    """Check files in S3 bucket."""
    print("=== S3 BUCKET STATUS ===")
    try:
        load_dotenv()
        session = boto3.Session(
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
            region_name=os.getenv('AWS_REGION')
        )
        s3_client = session.client('s3')
        bucket_name = os.getenv('S3_BUCKET_NAME')
        
        response = s3_client.list_objects_v2(Bucket=bucket_name)
        
        if 'Contents' in response:
            files = response['Contents']
            print(f"✅ S3 bucket '{bucket_name}' contains {len(files)} files:")
            for obj in files:
                size_mb = obj['Size'] / (1024*1024)
                print(f"  - {obj['Key']} ({size_mb:.2f} MB)")
        else:
            print(f"❌ S3 bucket '{bucket_name}' is empty")
            
    except Exception as e:
        print(f"❌ Error accessing S3: {e}")

def check_vector_store():
    """Check vector store contents."""
    print("\n=== VECTOR STORE STATUS ===")
    try:
        client = load_vectorstore_client()
        
        # Get collection info
        collection_info = client.get_collection('documents')
        print(f"✅ Collection 'documents' exists")
        print(f"  - Total points: {collection_info.points_count}")
        print(f"  - Vector configs: {list(collection_info.config.params.vectors.keys())}")
        
        # Get sample documents
        points, _ = client.scroll('documents', limit=5, with_payload=True)
        print(f"\n📄 Sample documents (showing {len(points)} of {collection_info.points_count}):")
        
        for i, point in enumerate(points, 1):
            payload = point.payload or {}
            source = payload.get('source', 'unknown')
            content = payload.get('content', '')
            is_image = payload.get('is_image', False)
            
            print(f"\n  {i}. ID: {point.id}")
            print(f"     Source: {source}")
            print(f"     Type: {'Image' if is_image else 'Text'}")
            print(f"     Content: {content[:100]}{'...' if len(content) > 100 else ''}")
            
            if is_image:
                print(f"     Image format: {payload.get('image_format', 'unknown')}")
                print(f"     Caption: {payload.get('image_caption', 'none')}")
        
    except Exception as e:
        print(f"❌ Error accessing vector store: {e}")

def check_environment():
    """Check environment configuration."""
    print("\n=== ENVIRONMENT CONFIGURATION ===")
    load_dotenv()
    
    required_vars = [
        'AWS_ACCESS_KEY_ID',
        'AWS_SECRET_ACCESS_KEY', 
        'AWS_REGION',
        'S3_BUCKET_NAME',
        'BEDROCK_EMBEDDING_MODEL_ID',
        'BEDROCK_LLM_MODEL_ID'
    ]
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            if 'KEY' in var:
                print(f"✅ {var}: {'*' * 20} (set)")
            else:
                print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: Not set")

def main():
    """Main function to run all checks."""
    print("🔍 INGESTION STATUS CHECK")
    print("=" * 50)
    
    check_environment()
    check_s3_files()
    check_vector_store()
    
    print("\n" + "=" * 50)
    print("✅ Ingestion check complete!")

if __name__ == "__main__":
    main()
