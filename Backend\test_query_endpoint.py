#!/usr/bin/env python3
"""
Script to test the query advanced endpoint functionality.
"""

import requests
import json
import time

BASE_URL = "http://localhost:8888"

def test_endpoint_health():
    """Test if the endpoint is accessible."""
    print("=== ENDPOINT HEALTH CHECK ===")
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=10)
        if response.status_code == 200:
            print("✅ Backend is running and accessible")
            return True
        else:
            print(f"❌ Backend returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to backend: {e}")
        return False

def test_simple_query():
    """Test a simple query to the advanced endpoint."""
    print("\n=== SIMPLE QUERY TEST ===")
    
    payload = {
        "question": "What is monitoring infrastructure?"
    }
    
    try:
        print(f"Sending request to: {BASE_URL}/query/advanced")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(
            f"{BASE_URL}/query/advanced",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Query successful!")
            print(f"Answer preview: {result.get('answer', 'No answer')[:200]}...")
            print(f"Sources found: {len(result.get('sources', []))}")
            print(f"Query type: {result.get('query_type', 'unknown')}")
            
            # Show first source if available
            sources = result.get('sources', [])
            if sources:
                print(f"First source: {sources[0].get('source', 'unknown')}")
                print(f"First source score: {sources[0].get('score', 'unknown')}")
            
            return True
        else:
            print(f"❌ Query failed with status {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False

def test_advanced_query_with_config():
    """Test query with retrieval configuration."""
    print("\n=== ADVANCED QUERY WITH CONFIG TEST ===")
    
    payload = {
        "question": "How to deploy alert rules in Kubernetes?",
        "retrieval_config": {
            "retriever_type": "hybrid",
            "use_mmr": True,
            "mmr_lambda": 0.7,
            "max_results": 5
        }
    }
    
    try:
        print(f"Sending advanced request to: {BASE_URL}/query/advanced")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(
            f"{BASE_URL}/query/advanced",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Advanced query successful!")
            print(f"Answer preview: {result.get('answer', 'No answer')[:200]}...")
            print(f"Sources found: {len(result.get('sources', []))}")
            print(f"Query type: {result.get('query_type', 'unknown')}")
            
            # Check if token usage is reported
            if 'token_usage' in result:
                token_usage = result['token_usage']
                print(f"Token usage - Input: {token_usage.get('input_tokens', 'N/A')}, Output: {token_usage.get('output_tokens', 'N/A')}")
            
            return True
        else:
            print(f"❌ Advanced query failed with status {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Advanced request failed: {e}")
        return False

def test_image_related_query():
    """Test a query that might return images."""
    print("\n=== IMAGE-RELATED QUERY TEST ===")
    
    payload = {
        "question": "Show me architecture diagrams or visual representations"
    }
    
    try:
        print(f"Sending image query to: {BASE_URL}/query/advanced")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(
            f"{BASE_URL}/query/advanced",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Image query successful!")
            print(f"Answer preview: {result.get('answer', 'No answer')[:200]}...")
            print(f"Sources found: {len(result.get('sources', []))}")
            
            # Check for images in response
            images = result.get('images', [])
            print(f"Images found: {len(images)}")
            
            if images:
                print("Image details:")
                for i, img in enumerate(images[:3]):  # Show first 3 images
                    print(f"  Image {i+1}: {img.get('source', 'unknown')}")
                    print(f"    Caption: {img.get('caption', 'none')}")
                    print(f"    Format: {img.get('format', 'unknown')}")
            
            return True
        else:
            print(f"❌ Image query failed with status {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Image request failed: {e}")
        return False

def test_error_handling():
    """Test error handling with invalid requests."""
    print("\n=== ERROR HANDLING TEST ===")
    
    # Test empty question
    payload = {"question": ""}
    
    try:
        response = requests.post(
            f"{BASE_URL}/query/advanced",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"Empty question response status: {response.status_code}")
        if response.status_code != 200:
            print("✅ Empty question properly rejected")
        else:
            print("⚠️ Empty question was accepted (might be handled gracefully)")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Empty question test failed: {e}")

def main():
    """Run all tests."""
    print("🔍 QUERY ADVANCED ENDPOINT TEST")
    print("=" * 50)
    
    # Test endpoint health first
    if not test_endpoint_health():
        print("\n❌ Backend is not accessible. Please check if it's running on port 8888.")
        return
    
    # Run all tests
    tests = [
        test_simple_query,
        test_advanced_query_with_config,
        test_image_related_query,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! Query advanced endpoint is working properly.")
    elif passed > 0:
        print("⚠️ Some tests passed. Endpoint is partially functional.")
    else:
        print("❌ All tests failed. Endpoint has issues.")

if __name__ == "__main__":
    main()
