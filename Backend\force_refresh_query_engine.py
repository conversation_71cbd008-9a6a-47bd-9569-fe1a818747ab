#!/usr/bin/env python3
"""
Force refresh the QueryEngine to pick up new environment variables.
"""

import requests
import json

def force_refresh_query_engine():
    """Force the backend to create a new QueryEngine instance."""
    print("🔄 Forcing QueryEngine refresh...")
    
    try:
        # Make a request to trigger QueryEngine initialization
        # We'll send a special request that forces re-initialization
        payload = {
            "question": "FORCE_REFRESH_QUERY_ENGINE_TEST",
            "retrieval_config": {"force_refresh": True}
        }
        
        response = requests.post(
            "http://localhost:8888/query/advanced",
            json=payload,
            timeout=60
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            answer = result.get('answer', '')
            
            if 'AWS Bedrock model' in answer and "couldn't access" in answer:
                print("❌ QueryEngine still has old configuration")
                return False
            else:
                print("✅ QueryEngine refreshed successfully!")
                return True
        else:
            print(f"❌ Request failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_normal_query():
    """Test with a normal query after refresh."""
    print("\n🔍 Testing normal query...")
    
    payload = {"question": "What is monitoring infrastructure?"}
    
    try:
        response = requests.post(
            "http://localhost:8888/query/advanced",
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            answer = result.get('answer', '')
            sources = result.get('sources', [])
            token_usage = result.get('token_usage', {})
            
            print(f"Sources found: {len(sources)}")
            
            if 'AWS Bedrock model' in answer and "couldn't access" in answer:
                print("❌ Still has Bedrock access issues")
                print(f"Answer: {answer[:100]}...")
                return False
            else:
                print("✅ Bedrock access working!")
                print(f"Answer preview: {answer[:200]}...")
                
                if token_usage:
                    model_name = token_usage.get('model_name', 'unknown')
                    total_tokens = token_usage.get('total_tokens', 'unknown')
                    print(f"Model used: {model_name}")
                    print(f"Total tokens: {total_tokens}")
                
                return True
        else:
            print(f"❌ Query failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🔧 FORCING QUERY ENGINE REFRESH")
    print("=" * 50)
    
    # First try to force refresh
    refresh_success = force_refresh_query_engine()
    
    # Then test with normal query
    query_success = test_normal_query()
    
    print("\n" + "=" * 50)
    if query_success:
        print("🎉 SUCCESS: Query Advanced endpoint is now working with corrected Bedrock configuration!")
    else:
        print("❌ ISSUE: QueryEngine still needs manual intervention")
        print("\nNext steps:")
        print("1. The global QueryEngine instance may be cached")
        print("2. Try restarting the backend completely")
        print("3. Or modify main.py to force QueryEngine re-initialization")
