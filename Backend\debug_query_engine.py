#!/usr/bin/env python3
"""
Debug the QueryEngine initialization to see what's happening.
"""

import os
from dotenv import load_dotenv

# Force reload environment
load_dotenv(override=True)

print("🔍 DEBUGGING QUERY ENGINE INITIALIZATION")
print("=" * 60)

print("Environment variables:")
print(f"  AWS_REGION: {os.getenv('AWS_REGION')}")
print(f"  BEDROCK_LLM_MODEL_ID: {os.getenv('BEDROCK_LLM_MODEL_ID')}")
print(f"  BEDROCK_LLM_PROFILE_ARN: {os.getenv('BEDROCK_LLM_PROFILE_ARN')}")

print("\nTesting QueryEngine initialization...")

try:
    from query import QueryEngine
    
    print("Creating new QueryEngine instance...")
    qe = QueryEngine()
    
    print("QueryEngine created successfully!")
    
    # Check LLM configuration
    if hasattr(qe, 'llm'):
        print(f"LLM type: {type(qe.llm)}")
        
        # Try to get model info
        if hasattr(qe.llm, 'model_id'):
            print(f"LLM model_id: {qe.llm.model_id}")
        
        # Test LLM directly
        print("\nTesting LLM directly...")
        try:
            response = qe.llm.invoke("Test message")
            print(f"✅ LLM test successful: {response.content[:100]}...")
        except Exception as e:
            print(f"❌ LLM test failed: {e}")
    
    # Test query_advanced method
    print("\nTesting query_advanced method...")
    try:
        result = qe.query_advanced("Test query")
        answer = result.get('answer', '')
        
        if 'AWS Bedrock model' in answer and "couldn't access" in answer:
            print("❌ query_advanced still has Bedrock issues")
        else:
            print("✅ query_advanced working!")
            
        print(f"Answer: {answer[:100]}...")
        
    except Exception as e:
        print(f"❌ query_advanced failed: {e}")
        
except Exception as e:
    print(f"❌ QueryEngine creation failed: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 60)
print("Debug complete.")
