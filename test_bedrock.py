#!/usr/bin/env python3
"""
Test AWS Bedrock connectivity and model access.
"""

import boto3
import json
import os
from dotenv import load_dotenv

def test_bedrock_access():
    """Test Bedrock model access with current credentials."""
    print("🔍 TESTING AWS BEDROCK ACCESS")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv('Backend/.env')
    
    # Get AWS configuration
    aws_access_key = os.getenv('AWS_ACCESS_KEY_ID')
    aws_secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
    aws_region = os.getenv('AWS_REGION')
    model_id = os.getenv('BEDROCK_LLM_MODEL_ID')
    profile_arn = os.getenv('BEDROCK_LLM_PROFILE_ARN')
    
    print(f"AWS Region: {aws_region}")
    print(f"Model ID: {model_id}")
    print(f"Profile ARN: {profile_arn}")
    print(f"Access Key: {aws_access_key[:10]}..." if aws_access_key else "Not set")
    
    try:
        # Create Bedrock runtime client
        bedrock_runtime = boto3.client(
            'bedrock-runtime',
            region_name=aws_region,
            aws_access_key_id=aws_access_key,
            aws_secret_access_key=aws_secret_key
        )
        
        print("\n✅ Bedrock runtime client created successfully")
        
        # Test 1: Direct model access
        print(f"\n1. Testing direct model access: {model_id}")
        
        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": [{"text": "Hello, this is a test. Please respond with 'Test successful'."}]
                }
            ],
            "inferenceConfig": {
                "maxTokens": 100,
                "temperature": 0.1
            }
        }
        
        try:
            response = bedrock_runtime.invoke_model(
                modelId=model_id,
                body=json.dumps(payload)
            )
            
            response_body = json.loads(response['body'].read())
            print("✅ Direct model access successful!")
            print(f"Response: {response_body}")
            
            # Extract the actual response text
            if 'output' in response_body and 'message' in response_body['output']:
                message_content = response_body['output']['message']['content']
                if message_content and len(message_content) > 0:
                    response_text = message_content[0].get('text', 'No text found')
                    print(f"Model response: {response_text}")
            
            return True, model_id
            
        except Exception as e:
            print(f"❌ Direct model access failed: {e}")
            
            # Test 2: Try with profile ARN if available
            if profile_arn and profile_arn != model_id:
                print(f"\n2. Testing with profile ARN: {profile_arn}")
                
                try:
                    response = bedrock_runtime.invoke_model(
                        modelId=profile_arn,
                        body=json.dumps(payload)
                    )
                    
                    response_body = json.loads(response['body'].read())
                    print("✅ Profile ARN access successful!")
                    print(f"Response: {response_body}")
                    
                    return True, profile_arn
                    
                except Exception as e2:
                    print(f"❌ Profile ARN access failed: {e2}")
            
            # Test 3: Try alternative models
            print(f"\n3. Testing alternative models...")
            
            alternative_models = [
                "anthropic.claude-3-haiku-20240307-v1:0",
                "anthropic.claude-3-sonnet-20240229-v1:0",
                "amazon.nova-micro-v1:0"
            ]
            
            for alt_model in alternative_models:
                print(f"   Testing: {alt_model}")
                try:
                    # For Claude models, use different payload format
                    if 'anthropic' in alt_model:
                        claude_payload = {
                            "anthropic_version": "bedrock-2023-05-31",
                            "max_tokens": 100,
                            "temperature": 0.1,
                            "messages": [
                                {
                                    "role": "user",
                                    "content": "Hello, this is a test. Please respond with 'Test successful'."
                                }
                            ]
                        }
                        test_payload = claude_payload
                    else:
                        test_payload = payload
                    
                    response = bedrock_runtime.invoke_model(
                        modelId=alt_model,
                        body=json.dumps(test_payload)
                    )
                    
                    response_body = json.loads(response['body'].read())
                    print(f"   ✅ {alt_model} works!")
                    print(f"   Response: {response_body}")
                    
                    return True, alt_model
                    
                except Exception as e3:
                    print(f"   ❌ {alt_model} failed: {e3}")
            
            return False, None
            
    except Exception as e:
        print(f"❌ Failed to create Bedrock client: {e}")
        return False, None

def generate_corrected_config(working_model_id):
    """Generate corrected .env configuration."""
    print(f"\n🔧 GENERATING CORRECTED CONFIGURATION")
    print("=" * 50)
    
    if working_model_id:
        print(f"✅ Working model found: {working_model_id}")
        
        # Determine if it's a profile ARN or direct model ID
        if 'inference-profile' in working_model_id:
            print("📝 Recommended .env configuration:")
            print(f"BEDROCK_LLM_MODEL_ID=amazon.nova-lite-v1:0")
            print(f"BEDROCK_LLM_PROFILE_ARN={working_model_id}")
        else:
            print("📝 Recommended .env configuration:")
            print(f"BEDROCK_LLM_MODEL_ID={working_model_id}")
            print(f"# BEDROCK_LLM_PROFILE_ARN=  # Not needed for direct model access")
    else:
        print("❌ No working model found")
        print("📝 Fallback configuration (try these models):")
        print("BEDROCK_LLM_MODEL_ID=anthropic.claude-3-haiku-20240307-v1:0")
        print("# BEDROCK_LLM_PROFILE_ARN=  # Comment out if not using profiles")

if __name__ == "__main__":
    success, working_model = test_bedrock_access()
    generate_corrected_config(working_model)
    
    if success:
        print(f"\n🎯 RESULT: AWS Bedrock is accessible with model: {working_model}")
        print("   Recommendation: Update .env file with the working configuration above")
    else:
        print(f"\n❌ RESULT: AWS Bedrock access failed")
        print("   Check AWS credentials and model permissions")
