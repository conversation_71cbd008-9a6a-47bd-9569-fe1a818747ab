#!/usr/bin/env python3
"""
Restart the backend with explicitly set environment variables.
"""

import os
import sys
import uvicorn

# Explicitly set the corrected environment variables
os.environ["AWS_REGION"] = "ap-south-1"
os.environ["BEDROCK_LLM_MODEL_ID"] = "amazon.nova-lite-v1:0"
os.environ["BEDROCK_LLM_PROFILE_ARN"] = "arn:aws:bedrock:ap-south-1:337909778990:inference-profile/apac.amazon.nova-lite-v1:0"

print("🔄 Starting backend with explicitly set environment variables...")
print(f"AWS_REGION: {os.environ['AWS_REGION']}")
print(f"BEDROCK_LLM_MODEL_ID: {os.environ['BEDROCK_LLM_MODEL_ID']}")
print(f"BEDROCK_LLM_PROFILE_ARN: {os.environ['BEDROCK_LLM_PROFILE_ARN']}")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8888,
        reload=True,
        log_level="info"
    )
