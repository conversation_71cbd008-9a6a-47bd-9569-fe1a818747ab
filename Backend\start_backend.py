#!/usr/bin/env python3
"""
Start the backend with explicit environment loading.
"""

import os
import sys
from dotenv import load_dotenv

# Force reload environment variables
load_dotenv(override=True)

print("🔄 Starting backend with corrected configuration...")
print(f"AWS_REGION: {os.getenv('AWS_REGION')}")
print(f"BEDROCK_LLM_MODEL_ID: {os.getenv('BEDROCK_LLM_MODEL_ID')}")
print(f"BEDROCK_LLM_PROFILE_ARN: {os.getenv('BEDROCK_LLM_PROFILE_ARN')}")

# Import and start uvicorn
import uvicorn

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8888,
        reload=True,
        log_level="info"
    )
