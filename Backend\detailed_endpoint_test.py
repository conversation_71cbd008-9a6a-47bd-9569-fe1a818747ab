#!/usr/bin/env python3
"""
Detailed test of the query advanced endpoint to check specific issues.
"""

import requests
import json

def test_detailed_response():
    """Get detailed response from the endpoint."""
    print("=== DETAILED ENDPOINT TEST ===")
    
    payload = {
        "question": "What is monitoring infrastructure?"
    }
    
    try:
        response = requests.post(
            "http://localhost:8888/query/advanced",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            
            print("\n=== RESPONSE STRUCTURE ===")
            print(f"Keys in response: {list(result.keys())}")
            
            print(f"\n=== ANSWER ===")
            answer = result.get('answer', 'No answer')
            print(f"Answer length: {len(answer)} characters")
            print(f"Answer: {answer}")
            
            print(f"\n=== SOURCES ===")
            sources = result.get('sources', [])
            print(f"Number of sources: {len(sources)}")
            
            for i, source in enumerate(sources[:3]):  # Show first 3 sources
                print(f"\nSource {i+1}:")
                print(f"  Source: {source.get('source', 'unknown')}")
                print(f"  Score: {source.get('score', 'unknown')}")
                print(f"  Content preview: {source.get('content_preview', 'No preview')[:100]}...")
                print(f"  Metadata keys: {list(source.keys())}")
            
            print(f"\n=== IMAGES ===")
            images = result.get('images', [])
            print(f"Number of images: {len(images)}")
            
            if images:
                for i, img in enumerate(images[:2]):  # Show first 2 images
                    print(f"\nImage {i+1}:")
                    print(f"  Source: {img.get('source', 'unknown')}")
                    print(f"  Caption: {img.get('caption', 'none')}")
                    print(f"  Format: {img.get('format', 'unknown')}")
                    print(f"  Has base64: {'base64_image' in img}")
                    print(f"  Metadata keys: {list(img.keys())}")
            
            print(f"\n=== OTHER FIELDS ===")
            for key, value in result.items():
                if key not in ['answer', 'sources', 'images']:
                    print(f"{key}: {value}")
                    
        else:
            print(f"Error response: {response.text}")
            
    except Exception as e:
        print(f"Request failed: {e}")

def test_aws_credentials_issue():
    """Test to understand the AWS credentials issue."""
    print("\n=== AWS CREDENTIALS DIAGNOSTIC ===")
    
    try:
        # Test if we can access the backend's diagnostic endpoint
        response = requests.get("http://localhost:8888/docs")
        if response.status_code == 200:
            print("✅ Backend is accessible")
        
        # Test a simple query to see the exact error
        payload = {"question": "test"}
        response = requests.post(
            "http://localhost:8888/query/advanced",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            answer = result.get('answer', '')
            
            if 'AWS Bedrock model' in answer:
                print("❌ AWS Bedrock LLM access issue detected")
                print("The retrieval is working but LLM generation is failing")
                
                # Check if sources are being retrieved
                sources = result.get('sources', [])
                if sources:
                    print(f"✅ Document retrieval is working ({len(sources)} sources found)")
                    print(f"First source: {sources[0].get('source', 'unknown')}")
                else:
                    print("❌ No sources retrieved")
            else:
                print("✅ Query processing appears to be working")
                
    except Exception as e:
        print(f"Diagnostic failed: {e}")

if __name__ == "__main__":
    test_detailed_response()
    test_aws_credentials_issue()
