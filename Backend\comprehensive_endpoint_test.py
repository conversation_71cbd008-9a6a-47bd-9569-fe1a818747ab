#!/usr/bin/env python3
"""
Comprehensive test of the query advanced endpoint functionality.
"""

import requests
import json
import time

BASE_URL = "http://localhost:8888"

def test_endpoint_functionality():
    """Comprehensive test of endpoint functionality."""
    print("🔍 COMPREHENSIVE QUERY ADVANCED ENDPOINT TEST")
    print("=" * 60)
    
    # Test 1: Basic connectivity
    print("\n1. TESTING BASIC CONNECTIVITY")
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is accessible")
        else:
            print(f"❌ Backend returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to backend: {e}")
        return False
    
    # Test 2: Document retrieval functionality
    print("\n2. TESTING DOCUMENT RETRIEVAL")
    payload = {"question": "What is monitoring infrastructure?"}
    
    try:
        response = requests.post(
            f"{BASE_URL}/query/advanced",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            sources = result.get('sources', [])
            
            if sources:
                print(f"✅ Document retrieval working ({len(sources)} sources found)")
                print(f"   Top source: {sources[0].get('source', 'unknown')}")
                print(f"   Score: {sources[0].get('score', 'unknown')}")
            else:
                print("❌ No sources retrieved")
                return False
        else:
            print(f"❌ Query failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False
    
    # Test 3: LLM response generation
    print("\n3. TESTING LLM RESPONSE GENERATION")
    answer = result.get('answer', '')
    
    if 'AWS Bedrock model' in answer and "couldn't access" in answer:
        print("❌ LLM access issue detected")
        print("   Issue: AWS Bedrock model configuration problem")
        print("   Impact: Retrieval works but answer generation fails")
        llm_working = False
    elif len(answer) > 50 and 'sorry' not in answer.lower():
        print("✅ LLM generating proper responses")
        llm_working = True
    else:
        print("⚠️ LLM response unclear")
        print(f"   Answer: {answer[:100]}...")
        llm_working = False
    
    # Test 4: Advanced retrieval configurations
    print("\n4. TESTING ADVANCED RETRIEVAL CONFIGURATIONS")
    
    configs_to_test = [
        {"retriever_type": "hybrid", "use_mmr": True},
        {"retriever_type": "fusion"},
        {"retriever_type": "multi_vector"},
        {"use_query_expansion": True},
        {"use_compression": True}
    ]
    
    config_results = []
    
    for i, config in enumerate(configs_to_test):
        try:
            test_payload = {
                "question": "How to deploy alert rules?",
                "retrieval_config": config
            }
            
            response = requests.post(
                f"{BASE_URL}/query/advanced",
                json=test_payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                sources_count = len(result.get('sources', []))
                retrieval_method = result.get('retrieval_method', 'unknown')
                
                config_results.append({
                    'config': config,
                    'sources_count': sources_count,
                    'method': retrieval_method,
                    'success': True
                })
                
                print(f"   ✅ Config {i+1}: {sources_count} sources, method: {retrieval_method}")
            else:
                config_results.append({
                    'config': config,
                    'success': False,
                    'error': response.status_code
                })
                print(f"   ❌ Config {i+1}: Failed with status {response.status_code}")
                
        except Exception as e:
            config_results.append({
                'config': config,
                'success': False,
                'error': str(e)
            })
            print(f"   ❌ Config {i+1}: Exception - {e}")
    
    successful_configs = sum(1 for r in config_results if r['success'])
    print(f"   Summary: {successful_configs}/{len(configs_to_test)} configurations working")
    
    # Test 5: Image retrieval
    print("\n5. TESTING IMAGE RETRIEVAL")
    
    image_payload = {"question": "Show me architecture diagrams"}
    
    try:
        response = requests.post(
            f"{BASE_URL}/query/advanced",
            json=image_payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            images = result.get('images', [])
            
            if images:
                print(f"✅ Image retrieval working ({len(images)} images found)")
                print(f"   Sample image: {images[0].get('source', 'unknown')}")
                print(f"   Format: {images[0].get('format', 'unknown')}")
            else:
                print("⚠️ No images found (may be expected)")
        else:
            print(f"❌ Image query failed with status {response.status_code}")
            
    except Exception as e:
        print(f"❌ Image query failed: {e}")
    
    # Test 6: Response structure validation
    print("\n6. TESTING RESPONSE STRUCTURE")
    
    expected_fields = [
        'answer', 'sources', 'query_type', 'retrieval_method',
        'template_used', 'context_quality', 'token_usage'
    ]
    
    missing_fields = []
    for field in expected_fields:
        if field not in result:
            missing_fields.append(field)
    
    if not missing_fields:
        print("✅ All expected response fields present")
    else:
        print(f"⚠️ Missing fields: {missing_fields}")
    
    # Test 7: Token usage tracking
    print("\n7. TESTING TOKEN USAGE TRACKING")
    
    token_usage = result.get('token_usage', {})
    if token_usage and 'total_tokens' in token_usage:
        print(f"✅ Token tracking working")
        print(f"   Total tokens: {token_usage.get('total_tokens', 'unknown')}")
        print(f"   Model: {token_usage.get('model_name', 'unknown')}")
    else:
        print("⚠️ Token usage tracking not working")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 ENDPOINT FUNCTIONALITY SUMMARY")
    print("=" * 60)
    
    functionality_status = {
        "Backend Connectivity": "✅ Working",
        "Document Retrieval": "✅ Working" if sources else "❌ Failed",
        "LLM Response Generation": "✅ Working" if llm_working else "❌ Failed",
        "Advanced Retrieval Configs": f"✅ {successful_configs}/{len(configs_to_test)} Working",
        "Image Retrieval": "✅ Working" if images else "⚠️ No images found",
        "Response Structure": "✅ Complete" if not missing_fields else "⚠️ Incomplete",
        "Token Tracking": "✅ Working" if token_usage else "⚠️ Not working"
    }
    
    for component, status in functionality_status.items():
        print(f"{component:.<30} {status}")
    
    # Overall assessment
    critical_issues = []
    if not sources:
        critical_issues.append("Document retrieval failed")
    if not llm_working:
        critical_issues.append("LLM response generation failed")
    
    print(f"\n🎯 OVERALL ASSESSMENT:")
    if not critical_issues:
        print("✅ Query Advanced endpoint is FULLY FUNCTIONAL")
    elif len(critical_issues) == 1 and "LLM response generation failed" in critical_issues:
        print("⚠️ Query Advanced endpoint is PARTIALLY FUNCTIONAL")
        print("   - Document retrieval and search working perfectly")
        print("   - LLM response generation needs AWS Bedrock configuration fix")
        print("   - Recommendation: Restart backend after fixing .env file")
    else:
        print("❌ Query Advanced endpoint has CRITICAL ISSUES")
        for issue in critical_issues:
            print(f"   - {issue}")
    
    return len(critical_issues) <= 1

if __name__ == "__main__":
    test_endpoint_functionality()
