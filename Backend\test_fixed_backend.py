#!/usr/bin/env python3
"""
Test the fixed backend configuration.
"""

import time
import requests
import json

def test_backend():
    print("🔍 Testing backend with corrected configuration...")
    
    # Wait for backend to start
    print("Waiting for backend to start...")
    time.sleep(10)
    
    try:
        # Test basic connectivity
        response = requests.get("http://localhost:8888/docs", timeout=10)
        if response.status_code != 200:
            print(f"❌ Backend not accessible (status: {response.status_code})")
            return
        
        print("✅ Backend is accessible")
        
        # Test query endpoint
        payload = {"question": "What is monitoring infrastructure?"}
        response = requests.post(
            "http://localhost:8888/query/advanced",
            json=payload,
            timeout=60
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            answer = result.get('answer', '')
            sources = result.get('sources', [])
            
            print(f"Sources found: {len(sources)}")
            
            if 'AWS Bedrock model' in answer and "couldn't access" in answer:
                print("❌ Still has Bedrock access issues")
                print("   Backend may need manual restart or environment reload")
            else:
                print("✅ Bedrock access working!")
                print(f"Answer preview: {answer[:200]}...")
                
                # Check token usage
                token_usage = result.get('token_usage', {})
                if token_usage:
                    model_name = token_usage.get('model_name', 'unknown')
                    print(f"Model used: {model_name}")
                    print(f"Tokens: {token_usage.get('total_tokens', 'unknown')}")
        else:
            print(f"❌ Query failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    test_backend()
