#!/usr/bin/env python3
"""
Comprehensive AWS Bedrock configuration verification and fix.
"""

import os
import json
import requests
from dotenv import load_dotenv
from langchain_aws import ChatBedrock

def verify_env_file():
    """Verify the .env file configuration."""
    print("🔍 VERIFYING .ENV FILE CONFIGURATION")
    print("=" * 60)
    
    load_dotenv('Backend/.env')
    
    config = {
        'AWS_REGION': os.getenv('AWS_REGION'),
        'AWS_ACCESS_KEY_ID': os.getenv('AWS_ACCESS_KEY_ID'),
        'AWS_SECRET_ACCESS_KEY': os.getenv('AWS_SECRET_ACCESS_KEY'),
        'BEDROCK_LLM_MODEL_ID': os.getenv('BEDROCK_LLM_MODEL_ID'),
        'BEDROCK_LLM_PROFILE_ARN': os.getenv('BEDROCK_LLM_PROFILE_ARN'),
        'BEDROCK_EMBEDDING_MODEL_ID': os.getenv('BEDROCK_EMBEDDING_MODEL_ID')
    }
    
    print("Current configuration:")
    for key, value in config.items():
        if 'KEY' in key:
            print(f"  {key}: {'*' * 20} (set)" if value else f"  {key}: NOT SET")
        else:
            print(f"  {key}: {value}")
    
    # Validate ARN format
    profile_arn = config['BEDROCK_LLM_PROFILE_ARN']
    if profile_arn:
        if profile_arn.count('arn:aws:bedrock:') == 1:
            print("✅ Profile ARN format is correct")
        else:
            print("❌ Profile ARN format is incorrect (contains duplicate prefixes)")
            return False
    
    return True

def test_direct_bedrock_access():
    """Test direct Bedrock access."""
    print("\n🔍 TESTING DIRECT BEDROCK ACCESS")
    print("=" * 60)
    
    load_dotenv('Backend/.env')
    
    try:
        llm = ChatBedrock(
            model_id=os.getenv('BEDROCK_LLM_PROFILE_ARN'),
            region_name=os.getenv('AWS_REGION'),
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
            provider="amazon",
            model_kwargs={"max_tokens": 100, "temperature": 0.1}
        )
        
        response = llm.invoke("Test message. Please respond with 'Bedrock access successful'.")
        print("✅ Direct Bedrock access working!")
        print(f"Response: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ Direct Bedrock access failed: {e}")
        return False

def test_backend_endpoint():
    """Test the backend endpoint."""
    print("\n🔍 TESTING BACKEND ENDPOINT")
    print("=" * 60)
    
    try:
        # Test basic connectivity
        response = requests.get("http://localhost:8888/docs", timeout=5)
        if response.status_code != 200:
            print(f"❌ Backend not accessible (status: {response.status_code})")
            return False
        
        print("✅ Backend is accessible")
        
        # Test query endpoint
        payload = {"question": "Test query for Bedrock verification"}
        response = requests.post(
            "http://localhost:8888/query/advanced",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            answer = result.get('answer', '')
            
            if 'AWS Bedrock model' in answer and "couldn't access" in answer:
                print("❌ Backend still has Bedrock access issues")
                print("   This indicates the backend needs to be restarted")
                return False
            else:
                print("✅ Backend Bedrock access working!")
                print(f"Answer preview: {answer[:100]}...")
                return True
        else:
            print(f"❌ Query endpoint failed (status: {response.status_code})")
            return False
            
    except Exception as e:
        print(f"❌ Backend test failed: {e}")
        return False

def generate_status_report():
    """Generate comprehensive status report."""
    print("\n📊 COMPREHENSIVE STATUS REPORT")
    print("=" * 60)
    
    # Run all tests
    env_ok = verify_env_file()
    bedrock_ok = test_direct_bedrock_access()
    backend_ok = test_backend_endpoint()
    
    print(f"\n🎯 SUMMARY:")
    print(f"  Environment Configuration: {'✅ OK' if env_ok else '❌ FAILED'}")
    print(f"  Direct Bedrock Access: {'✅ OK' if bedrock_ok else '❌ FAILED'}")
    print(f"  Backend Endpoint: {'✅ OK' if backend_ok else '❌ FAILED'}")
    
    # Provide recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    
    if not env_ok:
        print("  1. ❌ Fix .env file configuration")
        print("     - Check for duplicate ARN prefixes")
        print("     - Ensure all required variables are set")
    else:
        print("  1. ✅ .env file configuration is correct")
    
    if not bedrock_ok:
        print("  2. ❌ Fix AWS Bedrock access")
        print("     - Check AWS credentials")
        print("     - Verify model permissions")
        print("     - Test with AWS CLI: aws bedrock-runtime invoke-model")
    else:
        print("  2. ✅ AWS Bedrock access is working")
    
    if not backend_ok and bedrock_ok:
        print("  3. ❌ Backend needs restart")
        print("     - The backend is using old/cached configuration")
        print("     - Restart the backend to pick up corrected .env values")
        print("     - Command: Stop current backend and restart with:")
        print("       cd Backend && python -m uvicorn main:app --host 0.0.0.0 --port 8888 --reload")
    elif backend_ok:
        print("  3. ✅ Backend is working correctly")
    else:
        print("  3. ❌ Backend has multiple issues - fix Bedrock access first")
    
    # Overall status
    if env_ok and bedrock_ok and backend_ok:
        print(f"\n🎉 OVERALL STATUS: ✅ FULLY FUNCTIONAL")
        print("   All components are working correctly!")
    elif env_ok and bedrock_ok:
        print(f"\n⚠️ OVERALL STATUS: 🔄 NEEDS BACKEND RESTART")
        print("   Configuration is correct, but backend needs restart")
    else:
        print(f"\n❌ OVERALL STATUS: 🔧 NEEDS CONFIGURATION FIX")
        print("   Fix the issues above and retest")

def provide_correct_config():
    """Provide the correct configuration values."""
    print(f"\n📝 CORRECT CONFIGURATION VALUES")
    print("=" * 60)
    
    print("For Backend/.env file:")
    print("```")
    print("# AWS Configuration")
    print("AWS_ACCESS_KEY_ID=********************")
    print("AWS_SECRET_ACCESS_KEY=AFkXvxcW0qvedmm5CrwQae3zW0j9V4wrZ0GT0Lqj")
    print("AWS_REGION=ap-south-1")
    print("")
    print("# Bedrock Configuration")
    print("BEDROCK_EMBEDDING_MODEL_ID=amazon.titan-embed-text-v2:0")
    print("BEDROCK_LLM_MODEL_ID=amazon.nova-lite-v1:0")
    print("BEDROCK_LLM_PROFILE_ARN=arn:aws:bedrock:ap-south-1:************:inference-profile/apac.amazon.nova-lite-v1:0")
    print("```")
    
    print("\nKey points:")
    print("  - Profile ARN is required for Nova models (not direct model ID)")
    print("  - ARN format: arn:aws:bedrock:REGION:ACCOUNT:inference-profile/MODEL")
    print("  - No duplicate 'arn:aws:bedrock:' prefixes")
    print("  - Provider should be 'amazon' for Nova models in QueryEngine")

if __name__ == "__main__":
    print("🔧 AWS BEDROCK CONFIGURATION VERIFICATION")
    print("=" * 60)
    
    generate_status_report()
    provide_correct_config()
