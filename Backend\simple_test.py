import requests
import json

payload = {'question': 'What is monitoring infrastructure?'}
response = requests.post(
    'http://localhost:8888/query/advanced',
    json=payload,
    timeout=60
)

print(f'Status: {response.status_code}')
if response.status_code == 200:
    result = response.json()
    print(f'Sources: {len(result.get("sources", []))}')
    answer = result.get('answer', '')
    if 'AWS Bedrock model' in answer:
        print('LLM Issue: AWS Bedrock configuration problem')
    else:
        print('LLM Working: Generated proper response')
    print(f'Answer preview: {answer[:100]}...')
else:
    print(f'Error: {response.text}')
