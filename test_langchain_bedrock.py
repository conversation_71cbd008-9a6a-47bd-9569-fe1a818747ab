#!/usr/bin/env python3
"""
Test LangChain ChatBedrock with different configurations.
"""

import os
from dotenv import load_dotenv
from langchain_aws import ChatBedrock

def test_langchain_bedrock():
    """Test LangChain ChatBedrock with current configuration."""
    print("🔍 TESTING LANGCHAIN CHATBEDROCK")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv('Backend/.env')
    
    # Get configuration
    aws_region = os.getenv("AWS_REGION")
    aws_access_key = os.getenv("AWS_ACCESS_KEY_ID")
    aws_secret_key = os.getenv("AWS_SECRET_ACCESS_KEY")
    model_id = os.getenv("BEDROCK_LLM_MODEL_ID")
    profile_arn = os.getenv("BEDROCK_LLM_PROFILE_ARN")
    
    print(f"AWS Region: {aws_region}")
    print(f"Model ID: {model_id}")
    print(f"Profile ARN: {profile_arn}")
    
    # Test configurations
    configurations = [
        {
            "name": "Profile ARN with provider='amazon'",
            "config": {
                "model_id": profile_arn,
                "region_name": aws_region,
                "aws_access_key_id": aws_access_key,
                "aws_secret_access_key": aws_secret_key,
                "provider": "amazon",
                "model_kwargs": {"max_tokens": 100, "temperature": 0.1}
            }
        },
        {
            "name": "Profile ARN without provider",
            "config": {
                "model_id": profile_arn,
                "region_name": aws_region,
                "aws_access_key_id": aws_access_key,
                "aws_secret_access_key": aws_secret_key,
                "model_kwargs": {"max_tokens": 100, "temperature": 0.1}
            }
        },
        {
            "name": "Direct model ID",
            "config": {
                "model_id": model_id,
                "region_name": aws_region,
                "aws_access_key_id": aws_access_key,
                "aws_secret_access_key": aws_secret_key,
                "model_kwargs": {"max_tokens": 100, "temperature": 0.1}
            }
        }
    ]
    
    working_config = None
    
    for i, test_config in enumerate(configurations, 1):
        print(f"\n{i}. Testing: {test_config['name']}")
        
        try:
            # Create ChatBedrock instance
            llm = ChatBedrock(**test_config['config'])
            
            # Test with a simple message
            response = llm.invoke("Hello, this is a test. Please respond with 'Test successful'.")
            
            print(f"✅ Configuration works!")
            print(f"Response: {response.content}")
            
            working_config = test_config
            break
            
        except Exception as e:
            print(f"❌ Configuration failed: {e}")
    
    return working_config

def generate_query_engine_fix(working_config):
    """Generate the correct QueryEngine configuration."""
    print(f"\n🔧 QUERY ENGINE CONFIGURATION FIX")
    print("=" * 50)
    
    if working_config:
        print(f"✅ Working configuration: {working_config['name']}")
        
        config = working_config['config']
        
        print("\n📝 Recommended QueryEngine code fix:")
        print("```python")
        
        if 'provider' in config:
            print(f"llm_kwargs = dict(")
            print(f"    model_id=\"{config['model_id']}\",")
            print(f"    region_name=os.getenv(\"AWS_REGION\"),")
            print(f"    aws_access_key_id=os.getenv(\"AWS_ACCESS_KEY_ID\"),")
            print(f"    aws_secret_access_key=os.getenv(\"AWS_SECRET_ACCESS_KEY\"),")
            print(f"    provider=\"{config['provider']}\",")
            print(f"    model_kwargs={{\"max_tokens\": 1500, \"temperature\": 0.4}}")
            print(f")")
        else:
            print(f"llm_kwargs = dict(")
            print(f"    model_id=\"{config['model_id']}\",")
            print(f"    region_name=os.getenv(\"AWS_REGION\"),")
            print(f"    aws_access_key_id=os.getenv(\"AWS_ACCESS_KEY_ID\"),")
            print(f"    aws_secret_access_key=os.getenv(\"AWS_SECRET_ACCESS_KEY\"),")
            print(f"    model_kwargs={{\"max_tokens\": 1500, \"temperature\": 0.4}}")
            print(f")")
        
        print("```")
        
    else:
        print("❌ No working configuration found")

if __name__ == "__main__":
    working_config = test_langchain_bedrock()
    generate_query_engine_fix(working_config)
